<html>
<head>
  <link rel="stylesheet" href="/public/css/sticky.css" type="text/css" />
</head>
<%- include('./partials/header-home') %>
<body>
  <div class="main-content">
    <div class="sticky-container" id="events-container">
      <div class="sticky-element" id="events-sticky">
        <%- include('./partials/events') %>
      </div>
      <div class="sticky-placeholder" id="events-placeholder"></div>
    </div>
    <div class="content-area">
      <div class="main-banner">
        <div class="carousel-wrapper">
          <div class="carousel-slides">
            <% indexImages.forEach(function(image) { %>
              <a href="<%= image.url1 %>" target="_blank">
                <img src="<%= image.image %>" width="640" height="300" />
              </a>
              <% }); %>
          </div>
          <div class="carousel-indicators"></div>
        </div>
        <div class="sidebar">
          <% if (float1?.parameters?.html) { %>
            <div class="float1"><%- float1?.parameters?.html %></div>
          <% } %>
          <% if (float2?.parameters?.html) { %>
          <div class="float2"><%- float2?.parameters?.html %></div>
          <% } %>
        </div>
      </div>

      <div class="secondary-banner">
      <% if (bannerIndex === 0) { %>
        <%- footerbanner?.parameters?.html %>
      <% } %>
      </div>

      <div class="news-conetent">
        <div class="news-left">
          <%
          const adImages = [
            "https://mat.chasedream.com/chasedream/test/images/ad8.png",
            "https://mat.chasedream.com/chasedream/test/images/ad7.png",
            "https://mat.chasedream.com/chasedream/test/images/ad6.png"
          ];
          const adLinks = [
            "https://www.chasedream.com/1",
            "https://www.chasedream.com/2",
            "https://www.chasedream.com/3"
          ];
          %>
          <% articles.forEach(function(article, index) { %>
            <div class="news-item">
              <% if (article.type === 1) { %>
                <a href="<%= article.externalLink ? article.externalLink : '/article/' + article.id %>" target="_blank">
                  <img src="<%= article.pic %>" width="600" height="140" alt="<%= article.title %>" />
                </a>
              <% } else { %>
                <a href="<%= article.externalLink ? article.externalLink : '/article/' + article.id %>">
                  <img class="news-image" src="https://mat.chasedream.com/chasedream/test/images/400x240/<%= article.idx%>.png" />
                  <div class="news-content">
                    <h3><%= article.title %></h3>
                    <span class="news-subtitle"><%= article.summary %></span>
                    <div class="news-date">
                      <%= timestampToDate(article.datetime, 'MM-DD') %>
                    </div>
                  </div>
                </a>
              <% } %>
              <% if (index < adImages.length) { %>
                  <div class="news-ad">
                    <a href="<%= adLinks[index] %>" target="_blank">
                      <img src="<%= adImages[index] %>" width="260" height="140" />
                    </a>
                  </div>
                <% } %>
            </div>
            <% if (bannerIndex > 0 && bannerIndex === index + 1) { %>
              <div class="secondary-banner" style="margin: 20px 0;"><%- footerbanner?.parameters?.html %></div>
            <% } %>
          <% }); %>
        </div>
      </div>
    </div>
  </div>
  <%- include('./partials/footer') %>
  
  <!-- 登录弹出框 -->
  <div class="login-modal-overlay" id="login-modal-overlay">
    <div class="login-modal">      
      <svg class="login-modal-close" id="login-modal-close" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="width: 24px; height: 24px;"><g fill="none"><path d="M4.5 4.5L19.5 19.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M19.5 4.5L4.5 19.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>
      <iframe class="login-modal-iframe" src="<%= idUrl%>/login-www-pc?t=<%= Math.random() %>" frameborder="0"></iframe>
    </div>
  </div>
  
  <script src="/public/js/login-modal.js"></script>
</body>

<!-- 引入公共JavaScript文件 -->
<script src="/public/js/common.js"></script>
<script src="/public/js/sticky.js"></script>

</html>

import { ALL, Config, Controller, Get, Inject, Query } from '@midwayjs/decorator';
import { Context } from '@midwayjs/koa';
import { Param } from '@midwayjs/decorator';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { MoreThanOrEqual, Repository } from 'typeorm';
import { Utils } from './comm/utils';
import axios from 'axios';
const moment = require('moment-timezone');
import * as _ from 'lodash';
import { PortalArticleEntity } from './modules/base/entity/portal/Article';
import { PortalService } from './modules/base/service/common/portal';

/**
 * 欢迎界面
 */
@Controller('/')
export class WelcomeController {
  @Inject()
  ctx: Context;

  @Inject()
  utils: Utils;

  @Config('serviceUrl')
  serviceUrl;

  @InjectEntityModel(PortalArticleEntity)
  portalArticleEntity: Repository<PortalArticleEntity>;

  @Inject()
  portalService: PortalService;
  

  @Get('/', { summary: 'Home' })
  public async home() {
    let res = await axios.get(this.serviceUrl.showRelease);

    const events = res.data.data.events;
    const indexImages =
      [
        ...res.data.data.index_image.level1,
        ...res.data.data.index_image.level2,
        ...res.data.data.index_image.level3
      ];

    res = await axios.get(this.serviceUrl.wwwCDG);
    const category = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.category?.includes(category));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    //float
    let float1 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 1 && el?.parameters?.extra?.category?.includes(category));

    if (float1.length) {
      float1 = _.sample(float1);
    }

    let float2 = res.data.data.filter(el => el.type == 'float' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 2 && el?.parameters?.extra?.category?.includes(category));

    if (float2.length) {
      float2 = _.sample(float2);
    }

    //footerbanner
    let footerbanner = res.data.data.filter(el => el.type == 'footerbanner' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.position == 2 && el?.parameters?.extra?.category?.includes(category));

    if (footerbanner.length) {
      footerbanner = _.sample(footerbanner);
      footerbanner.parameters.html = footerbanner?.parameters?.html?.replace(/\s*style\s*=\s*["'][^"']*["']/gi, '');
    }

    const bannerIndex = await this.portalService.getPortalBannerIndex();

    const articles: any = await this.portalArticleEntity.find({
      where: {
        status: MoreThanOrEqual(0)
      },
      order: {
        displayOrder: 'DESC',
        id: 'DESC'
      },
      take: 10
    });

    let idx = 1;
    for(let article of articles) {
      article.idx = idx;
      idx++;
    }

    await this.ctx.render('www/index', {
      events,
      articles,
      indexImages,
      headerbanner,
      float1,
      float2,
      footerbanner,
      bannerIndex,
      category: 'Home',
      idUrl: this.serviceUrl.idUrl,
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
    });
  }

  @Get('/article/:id', { summary: 'Article' })
  public async article(@Param('id') id: number) {
    let res = await axios.get(this.serviceUrl.showRelease);

    const events = res.data.data.events;

    res = await axios.get(this.serviceUrl.wwwCDG);
    const category = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' && el?.targets?.indexOf('portal') != -1 && el?.parameters?.extra?.category?.includes(category));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    const article = await this.portalArticleEntity.findOneBy({ id });

    if (!article) {
      this.ctx.status = 404;
      return;
    }

    await this.ctx.render('www/article', {
      events,
      article,
      headerbanner,
      category: "",
      idUrl: this.serviceUrl.idUrl,
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
    });
  }

  @Get('/category/:category/:page?', { summary: 'Article' })
  public async category(
    @Param('category') category: string,
    @Param('page') pageParam?: string
  ) {
    let page = parseInt(pageParam || '1');
    const pageSize = 7;

    // Validate page number
    if (page < 1) {
      page = 1;
    }

    let res = await axios.get(this.serviceUrl.showRelease);
    const events = res.data.data.events;

    res = await axios.get(this.serviceUrl.wwwCDG);
    const cate = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' &&
      el?.targets?.indexOf('portal') != -1 &&
      el?.parameters?.extra?.category?.includes(cate));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    const [articles, total] = await this.portalArticleEntity.findAndCount({
      where: {
        category,
        status: MoreThanOrEqual(0)
      },
      order: {
        displayOrder: 'DESC',
        id: 'DESC'
      },
      skip: (page - 1) * pageSize,
      take: pageSize
    });

    let idx = 1;
    for(let article of articles) {
      //@ts-ignore
      article.idx = idx;
      idx++;
    }

    const totalPages = Math.ceil(total / pageSize);

    await this.ctx.render('www/category', {
      events,
      articles,
      headerbanner,
      category,
      pagination: {
        current: page,
        total: totalPages,
        pageSize,
        totalItems: total
      },
      idUrl: this.serviceUrl.idUrl,
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
    });
  }

  @Get('/search', { summary: 'Search' })
  public async search(@Query(ALL) params: any) {
    let res = await axios.get(this.serviceUrl.showRelease);
    const events = res.data.data.events;

    res = await axios.get(this.serviceUrl.wwwCDG);
    const cate = '-1';

    //headerbanner
    let headerbanner = res.data.data.filter(el => el.type == 'headerbanner' &&
      el?.targets?.indexOf('portal') != -1 &&
      el?.parameters?.extra?.category?.includes(cate));

    if (headerbanner.length) {
      headerbanner = _.sample(headerbanner);
    }

    if (!params.q?.trim()) {
      await this.ctx.render('www/search', {
        events,
        articles: [],
        headerbanner,
        category: "",
        q: '',
        pagination: {
          current: 1,
          total: 0,
          totalItems: 0
        },
        formatDateTime: this.utils.formatDateTime,
        timestampToDate: this.utils.timestampToDate,
        newFlag: this.utils.newFlag,
      });
      return;
    }

    let page = parseInt(params.page || '1');
    if (page < 1) {
      page = 1;
    }

    const {list, pagination} = await this.portalService.search(params);

    const totalPages = Math.ceil(pagination.total / pagination.size);

    await this.ctx.render('www/search', {
      events,
      articles: list,
      headerbanner,
      category: "",
      q: params.q,
      pagination: {
        current: page,
        total: totalPages,
        totalItems: pagination.total
      },
      formatDateTime: this.utils.formatDateTime,
      timestampToDate: this.utils.timestampToDate,
      newFlag: this.utils.newFlag,
    });
  }

  @Get(/^\/[Pp][Rr][Ii][Vv][Aa][Cc][Yy]\.html$/i, { summary: 'Privacy Policy' })
  public async privacy() {
    await this.ctx.render('www/Privacy');
  }

  @Get(/^\/[Pp][Rr][Ii][Vv][Aa][Cc][Yy]-[Hh][Ww]\.html$/i, { summary: 'Privacy Policy HW' })
  public async privacyHW() {
    await this.ctx.render('www/Privacy-HW');
  }
}
